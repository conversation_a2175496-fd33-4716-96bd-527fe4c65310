import React, { useRef, useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  motion,
  useMotionValue,
  useSpring,
  useTransform,
  AnimatePresence,
} from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface Subsegment {
  name: string;
  href: string;
}

interface NavItem {
  name: string;
  href: string;
  subsegments?: Subsegment[];
}

interface DockNavItemProps {
  item: NavItem;
  mouseX: any;
  spring: any;
  distance: number;
  magnification: number;
  baseItemSize: number;
  isActive: boolean;
  hoveredSegment: string | null;
  setHoveredSegment: (segment: string | null) => void;
  handleSectionScroll?: (href: string) => void;
}

function DockNavItem({
  item,
  mouseX,
  spring,
  distance,
  magnification,
  baseItemSize,
  isActive,
  hoveredSegment,
  setHoveredSegment,
  handleSectionScroll,
}: DockNavItemProps) {
  const ref = useRef<HTMLAnchorElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  const mouseDistance = useTransform(mouseX, (val) => {
    const rect = ref.current?.getBoundingClientRect() ?? {
      x: 0,
      width: baseItemSize,
    };
    return val - rect.x - rect.width / 2;
  });

  const targetScale = useTransform(
    mouseDistance,
    [-distance, 0, distance],
    [1, magnification, 1]
  );
  const scale = useSpring(targetScale, spring);

  const targetY = useTransform(
    mouseDistance,
    [-distance, 0, distance],
    [0, -4, 0]
  );
  const y = useSpring(targetY, spring);

  const hasSubsegments = item.subsegments && item.subsegments.length > 0;
  const isSegmentHovered = hoveredSegment === item.name;

  return (
    <motion.div
      style={{
        scale,
        y,
      }}
      className="relative"
      onMouseEnter={() => {
        setIsHovered(true);
        if (hasSubsegments) {
          setHoveredSegment(item.name);
        }
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        if (hasSubsegments) {
          setHoveredSegment(null);
        }
      }}
    >
      <Link
        ref={ref}
        to={item.href}
        className={`relative px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg block cursor-pointer flex items-center space-x-1 ${
          isActive
            ? 'text-green-400 bg-green-400/15 shadow-lg shadow-green-400/20'
            : 'text-gray-300 hover:text-green-400 hover:bg-green-400/10 hover:shadow-md'
        }`}
      >
        <span>{item.name}</span>
        {hasSubsegments && (
          <motion.div
            animate={{ rotate: isSegmentHovered ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-3 h-3" />
          </motion.div>
        )}

      </Link>

      {/* Subsegments Dropdown */}
      <AnimatePresence>
        {isSegmentHovered && hasSubsegments && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl z-50"
            onMouseEnter={() => setHoveredSegment(item.name)}
            onMouseLeave={() => setHoveredSegment(null)}
          >
            <div className="py-2">
              {item.subsegments!.map((subsegment, index) => (
                <motion.div
                  key={subsegment.name}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  {subsegment.href.includes('#') ? (
                    <button
                      onClick={() => handleSectionScroll?.(subsegment.href)}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-green-400 hover:bg-green-400/10 transition-all duration-200"
                    >
                      {subsegment.name}
                    </button>
                  ) : (
                    <Link
                      to={subsegment.href}
                      className="block px-4 py-2 text-sm text-gray-300 hover:text-green-400 hover:bg-green-400/10 transition-all duration-200"
                    >
                      {subsegment.name}
                    </Link>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

interface DockNavbarProps {
  navigation: NavItem[];
  className?: string;
  spring?: any;
  magnification?: number;
  distance?: number;
  baseItemSize?: number;
  hoveredSegment?: string | null;
  setHoveredSegment?: (segment: string | null) => void;
  handleSectionScroll?: (href: string) => void;
}

export default function DockNavbar({
  navigation,
  className = "",
  spring = { mass: 0.1, stiffness: 200, damping: 15 },
  magnification = 1.1,
  distance = 80,
  baseItemSize = 40,
  hoveredSegment = null,
  setHoveredSegment = () => {},
  handleSectionScroll = () => {},
}: DockNavbarProps) {
  const mouseX = useMotionValue(Infinity);
  const location = useLocation();

  return (
    <div
      onMouseMove={({ pageX }) => {
        mouseX.set(pageX);
      }}
      onMouseLeave={() => {
        mouseX.set(Infinity);
        setHoveredSegment(null);
      }}
      className={`flex items-center space-x-2 ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      {navigation.map((item) => (
        <DockNavItem
          key={item.name}
          item={item}
          mouseX={mouseX}
          spring={spring}
          distance={distance}
          magnification={magnification}
          baseItemSize={baseItemSize}
          isActive={location.pathname === item.href}
          hoveredSegment={hoveredSegment}
          setHoveredSegment={setHoveredSegment}
          handleSectionScroll={handleSectionScroll}
        />
      ))}
    </div>
  );
}
